# -*- coding: utf-8 -*-
"""
测试页面结构脚本 - 用于分析网站DOM结构
"""

import requests
from bs4 import BeautifulSoup
from lxml import html
import re

def analyze_page_structure():
    """分析页面结构"""
    url = "https://www.njnii.com/channel.aspx?id=3"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print(f"正在访问: {url}")
        # 禁用代理
        proxies = {'http': None, 'https': None}
        response = requests.get(url, headers=headers, timeout=30, proxies=proxies)
        response.raise_for_status()
        
        if response.encoding == 'ISO-8859-1':
            response.encoding = response.apparent_encoding or 'utf-8'
        elif not response.encoding:
            response.encoding = 'utf-8'
            
        print(f"页面获取成功，状态码: {response.status_code}")
        print(f"页面编码: {response.encoding}")
        
        # 使用BeautifulSoup解析
        soup = BeautifulSoup(response.text, 'html.parser')
        title = soup.find('title')
        print(f"页面标题: {title.get_text().strip() if title else '未找到标题'}")
        
        # 查找包含日期的元素
        print("\n=== 查找包含2025年日期的元素 ===")
        date_elements = soup.find_all(text=re.compile(r'2025-\d{2}-\d{2}'))
        print(f"找到 {len(date_elements)} 个包含2025年日期的文本节点")
        
        for i, elem in enumerate(date_elements[:5]):  # 只显示前5个
            parent = elem.parent if elem.parent else None
            print(f"{i+1}. 日期文本: {elem.strip()}")
            if parent:
                print(f"   父元素: {parent.name} - {parent.get('class', [])} - {parent.get('id', '')}")
                # 查找同级或父级的链接
                links = parent.find_all('a', href=True)
                for link in links:
                    print(f"   相关链接: {link.get('href')} - {link.get_text().strip()[:50]}")
            print()
        
        # 查找新闻列表容器
        print("\n=== 查找可能的新闻列表容器 ===")
        
        # 查找包含多个链接的div
        divs_with_links = soup.find_all('div', class_=True)
        for div in divs_with_links:
            links = div.find_all('a', href=True)
            if len(links) >= 3:  # 包含3个以上链接的div可能是新闻列表
                print(f"发现容器: {div.name} - class: {div.get('class')} - id: {div.get('id')}")
                print(f"  包含 {len(links)} 个链接")
                for link in links[:3]:  # 显示前3个链接
                    print(f"    链接: {link.get('href')} - {link.get_text().strip()[:30]}")
                print()
        
        # 使用lxml分析XPath
        print("\n=== 使用lxml分析结构 ===")
        tree = html.fromstring(response.text)
        
        # 尝试不同的XPath来查找新闻项
        xpath_patterns = [
            "//div[contains(text(), '2025-')]",
            "//li[contains(text(), '2025-')]", 
            "//*[contains(text(), '2025-')]",
            "//div[@class]//div[contains(text(), '2025-')]",
            "//ul//li[contains(text(), '2025-')]"
        ]
        
        for xpath in xpath_patterns:
            try:
                elements = tree.xpath(xpath)
                print(f"XPath: {xpath}")
                print(f"  找到 {len(elements)} 个元素")
                
                for i, elem in enumerate(elements[:3]):  # 只显示前3个
                    if hasattr(elem, 'text_content'):
                        text = elem.text_content().strip()[:100]
                        print(f"    {i+1}. {text}")
                        
                        # 查找相关链接
                        links = elem.xpath('.//a[@href]')
                        for link in links:
                            href = link.get('href')
                            link_text = link.text_content().strip()[:50]
                            print(f"       链接: {href} - {link_text}")
                print()
            except Exception as e:
                print(f"XPath {xpath} 执行失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"分析页面结构失败: {e}")
        return False

if __name__ == "__main__":
    analyze_page_structure()
