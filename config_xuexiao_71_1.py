'''
Description: 南京市南京工业大学新闻爬虫配置文件
Date: 2025-07-18
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-07-18 16:08:24
FilePath: \Spider_faxuan\config_xuexiao_71_1 copy.py
'''
# -*- coding: utf-8 -*-
"""
南京市南京工业大学新闻分页爬虫配置文件
"""

# 起始页面配置
START_URL = "https://www.njtech.edu.cn/index/ngyw.htm"
BASE_URL = "https://www.njtech.edu.cn"
CURRENT_URL = "https://www.njtech.edu.cn/index/"

# XPath配置
LIST_CONTAINER_XPATH = "//ul"  # 新闻列表容器XPath
NEWS_ITEM_XPATH = "//li[contains(., '2025-')]"  # 新闻项XPath（包含日期的li元素）
NEWS_TITLE_XPATH = ".//a[contains(@href, '.htm')]"  # 新闻标题链接XPath（选择htm链接）
NEWS_DATE_XPATH = ".//text()[contains(., '2025-')]"  # 新闻日期XPath（包含2025-的文本）
NEXT_PAGE_XPATH = "//a[contains(text(), '下页')]/@href"  # 下一页链接XPath

# 分页URL模式配置 - 南京工业大学使用递减页码模式
PAGE_URL_PATTERN = "https://www.njtech.edu.cn/index/ngyw/{}.htm"  # 分页URL模式，{}为页码占位符
FIRST_PAGE_URL = "https://www.njtech.edu.cn/index/ngyw.htm"  # 第一页URL
STARTING_PAGE_NUMBER = 116  # 第二页开始的页码（递减模式）

# 爬取配置
MAX_PAGES = 15  # 最多爬取页数（增加页数以确保覆盖5-7月的所有新闻）
START_PAGE = 0  # 起始页码

# 目标日期范围配置
TARGET_DATE_RANGE = ['2025-05', '2025-06', '2025-07']  # 爬取5-7月的新闻

# 输出文件配置
OUTPUT_FILENAME = "spiderdata/南京工业大学_南工要闻_5-7月.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证
VERIFY_SSL = True

# 代理设置
PROXIES = {'http': None, 'https': None}

# 内容提取XPath配置（用于详情页）- 针对南京工业大学网站优化
CONTENT_XPATHS = [
    "//div[contains(@class, 'content')]//p//text()",  # 内容区域段落文本
    "//div[contains(@class, 'main')]//p//text()",  # 主要内容区域段落文本
    "//body//p[string-length(text()) > 20]//text()",  # 长度超过20字符的段落文本
    "//*[not(self::script or self::style or self::nav or self::header or self::footer)]/text()[string-length(normalize-space(.)) > 30]",  # 排除导航等的长文本
    "//text()[string-length(normalize-space(.)) > 50]",  # 长文本节点
]

# 标题提取XPath配置 - 针对南京工业大学网站优化
TITLE_XPATHS = [
    "//h4",  # 南京工业大学详情页使用h4作为标题
    "//h1",
    "//h2",
    "//h3",
    "//title",
    "//div[@class='title']",
    "//div[contains(@class, 'title')]",
]

# 发表时间提取XPath配置 - 针对南京工业大学网站优化
PUBLISH_TIME_XPATHS = [
    "//text()[contains(., '时间：') and contains(., '2025-')]",  # 时间：2025-xx-xx格式
    "//text()[contains(., '2025-')]",  # 包含2025-的文本节点
    "//span[contains(text(), '2025')]",  # 包含2025的span
    "//div[contains(text(), '2025')]",  # 包含2025的div
    "//p[contains(text(), '2025')]",  # 包含2025的段落
    "//*[contains(text(), '来源：')]",  # 包含来源的元素
]
