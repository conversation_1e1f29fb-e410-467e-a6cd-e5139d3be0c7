# -*- coding: utf-8 -*=
"""
南京市委组织部工作动态爬虫
适配新URL: https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html
专门爬取2025年5-7月工作动态内容
"""
import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html
import re
from datetime import datetime
import os
import sys
import time
from urllib.parse import urljoin, urlparse

try:
    from openpyxl.styles import Alignment
except ImportError:
    Alignment = None

from config_new_3_1 import (
    START_URL, BASE_URL, LIST_CONTAINER_XPATH, NEWS_ITEM_XPATH, 
    NEWS_TITLE_XPATH, NEWS_DATE_XPATH, NEXT_PAGE_XPATH,
    PAGE_URL_PATTERNS, FIRST_PAGE_URL, MAX_PAGES, START_PAGE,
    TARGET_DATE_RANGE, OUTPUT_FILENAME, HEADERS, REQUEST_TIMEOUT, 
    MAX_RETRIES, RETRY_DELAY, VERIFY_SSL, PROXIES
)

class GzdtSpider:
    """南京市委组织部工作动态爬虫类"""
    
    def __init__(self):
        """初始化组织部爬虫"""
        self.start_url = START_URL
        self.base_url = BASE_URL
        
        # XPath选择器
        self.list_container_xpath = LIST_CONTAINER_XPATH
        self.news_item_xpath = NEWS_ITEM_XPATH
        self.news_title_xpath = NEWS_TITLE_XPATH
        self.news_date_xpath = NEWS_DATE_XPATH
        self.next_page_xpath = NEXT_PAGE_XPATH
        
        # 分页配置
        self.page_url_patterns = PAGE_URL_PATTERNS
        self.first_page_url = FIRST_PAGE_URL
        self.max_pages = MAX_PAGES
        self.start_page = START_PAGE
        self.current_pattern = None  # 动态确定有效的分页模式
        
        # 筛选和输出配置
        self.target_date_range = TARGET_DATE_RANGE
        self.output_file = OUTPUT_FILENAME
        
        # 网络配置
        self.headers = HEADERS
        self.timeout = REQUEST_TIMEOUT
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY
        self.verify_ssl = VERIFY_SSL
        self.proxies = PROXIES
        
        # 内容提取配置
        self.content_xpaths = [
            "//div[@class='content_text']//p",
            "//div[@class='TRS_Editor']//p",
            "//div[@class='article-content']//p",
            "//div[@id='zoom']//p",
            "//div[@class='padding']//p",
            "//body//p[string-length(text()) > 30]"
        ]
        self.title_xpaths = [
            "//h1",
            "//h2[@class='title']",
            "//div[@class='title']//h2", 
            "//div[@class='tit']",
            "//title"
        ]
        self.publish_time_xpaths = [
            "//span[@class='time']",
            "//span[@class='date']",
            "//div[@class='article-infos']//span",
            "//*[contains(text(), '发布时间')]",
            "//*[contains(text(), '发布日期')]",
            "//text()[contains(.,'2025-') or contains(.,'2025/') or contains(.,'2025年')]"
        ]
        
        # 统计信息
        self.processed_articles = 0
        self.filtered_articles = 0
        
    def fetch_webpage(self, url):
        """获取网页内容（带重试机制）"""
        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    print(f"  第 {attempt + 1} 次重试...")
                    time.sleep(self.retry_delay)

                request_params = {
                    'headers': self.headers,
                    'timeout': self.timeout,
                    'verify': self.verify_ssl,
                    'proxies': {'http': None, 'https': None}  # 强制禁用代理
                }

                response = requests.get(url, **request_params)
                response.raise_for_status()

                # 智能编码检测
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'
                elif not response.encoding:
                    response.encoding = 'utf-8'

                # 获取页面标题用于调试
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.get_text().strip() if title else "未获取到标题"

                print(f"  ✓ 页面获取成功: {page_title}")
                return True, response.text, page_title

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (超过{self.timeout}秒)"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.ConnectionError as e:
                error_msg = f"网络连接错误: {str(e)}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, "网络连接错误，请检查网络", ""

            except requests.exceptions.HTTPError as e:
                error_msg = f"HTTP错误: {e}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except Exception as e:
                error_msg = f"获取页面失败: {str(e)}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

        return False, "所有重试失败", ""

    def extract_links_from_page(self, html_content):
        """从页面提取工作动态链接，支持日期筛选 - 修正版"""
        try:
            # 使用BeautifulSoup解析，更稳定
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有包含链接和日期的li元素
            news_items = soup.find_all('li')

            if not news_items:
                print(f"  ⚠️  未找到任何li元素")
                return []

            print(f"  📋 共找到 {len(news_items)} 个li元素，开始筛选...")

            valid_links = []

            for idx, item in enumerate(news_items, 1):
                try:
                    # 检查是否包含链接
                    link_element = item.find('a', href=True)
                    if not link_element:
                        continue

                    title_text = link_element.get_text().strip()
                    title_link = link_element.get('href')

                    if not title_text or not title_link:
                        continue

                    # 在li元素的文本中查找日期
                    item_text = item.get_text()
                    date_match = re.search(r'2025-(\d{2})-(\d{2})', item_text)

                    if not date_match:
                        continue

                    date_text = date_match.group()  # 如: 2025-07-04
                    month = int(date_match.group(1))  # 提取月份

                    # 检查是否在5-7月范围内
                    if month not in [5, 6, 7]:
                        self.filtered_articles += 1
                        print(f"    跳过第{idx}条: 日期{date_text}不在5-7月范围内")
                        continue

                    # 处理相对URL
                    if not title_link.startswith('http'):
                        # 清理路径开头字符
                        clean_link = title_link.lstrip('./')
                        title_link = urljoin("https://zzb.nanjing.gov.cn/lgbgz/gzdt/", clean_link)

                    valid_links.append({
                        'url': title_link,
                        'title': title_text,
                        'date': date_text,
                        'date_key': f"2025-{month:02d}"
                    })

                    print(f"    ✅ 找到目标文章: {title_text[:30]}... ({date_text})")

                except Exception as e:
                    print(f"    处理第{idx}条时出错: {str(e)}")
                    continue

            print(f"  ✅ 筛选后找到 {len(valid_links)} 条目标日期新闻")
            return valid_links

        except Exception as e:
            print(f"  ❌ 提取链接失败: {str(e)}")
            return []

    def is_valid_date_format(self, date_text):
        """验证日期格式是否有效"""
        if not date_text:
            return False
        date_patterns = [
            r'2025[年\-./]?0?[5-7][月\-./]?[\d\-]*',
            r'0?[5-7][月\-./][2025]*'
        ]
        return any(re.search(pattern, date_text) for pattern in date_patterns)

    def extract_date_key(self, date_text):
        """从日期文本中提取标准化日期键值"""
        if not date_text:
            return None
            
        # 统一清理格式
        date_text = re.sub(r'[年月日/\.年]', '-', date_text)
        
        # 提取2025-05或2025-5格式
        matches = re.findall(r'2025\-?0?([5-7])', date_text)
        if matches:
            month = int(matches[0])
            return f"2025-{month:02d}"
        
        return None

    def is_in_target_range(self, date_key):
        """检查日期是否在目标范围内"""
        return any(target in date_key for target in ['2025-05', '2025-06', '2025-07'])

    def extract_article_content(self, url):
        """提取文章内容（标题、正文、发布日期）- 修正版"""
        print(f"  📄 正在解析文章: {url}")

        success, html_content, _ = self.fetch_webpage(url)
        if not success:
            print(f"    ❌ 无法获取文章页面: {html_content}")
            return "", "", ""

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取标题 - 多种方法
            title = ""

            # 方法1: 查找加粗的标题
            bold_title = soup.find('strong')
            if bold_title:
                title = bold_title.get_text().strip()

            # 方法2: 从页面文本中提取标题（在"当前位置"之后，"来源"之前）
            if not title:
                all_text = soup.get_text()
                lines = all_text.split('\n')

                for i, line in enumerate(lines):
                    line = line.strip()
                    # 查找"当前位置"后面的标题
                    if '当前位置：' in line and i + 1 < len(lines):
                        # 标题通常在当前位置后面几行
                        for j in range(i + 1, min(i + 5, len(lines))):
                            potential_title = lines[j].strip()
                            if (len(potential_title) > 5 and
                                '来源:' not in potential_title and
                                '发布时间:' not in potential_title and
                                potential_title not in ['首页', '老干部工作', '工作动态']):
                                title = potential_title
                                break
                        if title:
                            break

            # 提取正文 - 简化方法
            content = ""
            all_text = soup.get_text()
            lines = all_text.split('\n')
            content_lines = []

            start_collecting = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 开始收集内容的标志
                if '发布时间:' in line:
                    start_collecting = True
                    continue

                # 停止收集的标志
                if '版权所有' in line or '苏ICP备' in line:
                    break

                # 收集有效内容
                if start_collecting and len(line) > 15:
                    # 过滤掉一些无用信息
                    if (line not in ['搜索', '首页', '组织建设', '干部工作', '公务员工作', '人才工作', '老干部工作'] and
                        '欢迎光临' not in line and
                        'javascript:' not in line):
                        content_lines.append(line)

            content = '\n\n'.join(content_lines)

            # 提取发布时间
            publish_time = ""
            time_match = re.search(r'发布时间:\s*(2025-\d{2}-\d{2})', all_text)
            if time_match:
                publish_time = time_match.group(1)

            return self.clean_text(title), self.clean_text(content), publish_time

        except Exception as e:
            print(f"    ❌ 解析文章内容失败: {str(e)}")
            return "", "", ""

    def clean_text(self, text):
        """高级文本清理"""
        if not text:
            return ""
        
        try:
            # HTML标签清理
            text = re.sub(r'<[^>]+>', '', text)
            
            # HTML实体转义
            entity_map = {
                '&nbsp;': ' ', '&lt;': '<', '&gt;': '>', 
                '&amp;': '&', '&quot;': '"', '&#39;': "'",
                '&hellip;': '...', '&mdash;': '—', '&ndash;': '–'
            }
            
            for entity, char in entity_map.items():
                text = text.replace(entity, char)
            
            # 空白字符标准化
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'[\r\n]+', '\n\n', text)
            
            return text.strip()
            
        except Exception:
            return str(text).strip()

    def construct_next_url(self, current_page):
        """构造下一页URL（动态选择有效模式）"""
        if not self.current_pattern and current_page <= 2:
            # 前2页用来探测有效的分页模式
            for pattern in self.page_url_patterns:
                test_url = pattern.format(current_page)
                print(f"    📋 测试分页模式: {test_url}")
                success, _, _ = self.fetch_webpage(test_url)
                if success:
                    self.current_pattern = pattern
                    print(f"    ✅ 确定分页模式: {pattern}")
                    return test_url
            
            # 找不到有效模式时使用第一页
            print("    ⚠️  找不到有效分页模式，仅爬取第一页")
            return None
            
        elif self.current_pattern:
            return self.current_pattern.format(current_page)
        
        return None

    def crawl_all_pages(self):
        """主爬取流程"""
        print("🚀 开始爬取南京市委组织部工作动态...")
        print(f"📝 目标日期: 2025年5-7月")
        print(f"📂 输出文件: {self.output_file}")
        print("=" * 60)

        all_articles = []
        
        # 创建输出目录
        output_dir = os.path.dirname(self.output_file) or "spiderdata"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        page = self.start_page
        
        while page <= self.max_pages:
            current_url = self.construct_next_url(page) if page > 1 else self.first_page_url
            if not current_url:
                break
                
            print(f"📄 正在处理第 {page} 页: {current_url}")
            
            success, html_content, page_title = self.fetch_webpage(current_url)
            if not success:
                print(f"  ❌ 获取页面失败，跳过: {html_content}")
                page += 1
                continue

            # 提取新闻链接
            articles = self.extract_links_from_page(html_content)
            if not articles:
                print("  ⚠️  本页无目标新闻，可能已到最后一页")
                break

            # 逐条处理文章
            for article in articles:
                title, content, publish_time = self.extract_article_content(article['url'])

                # 修正判断条件：只要有内容就保存，标题可以用原标题
                if content and len(content) > 50:
                    final_title = title if title else article['title']  # 如果没提取到标题，用原标题

                    all_articles.append({
                        '标题': final_title,
                        '发布日期': publish_time or article['date'],
                        '原日期': article['date'],
                        '链接': article['url'],
                        '正文': content,
                        '字数': len(content)
                    })

                    self.processed_articles += 1
                    print(f"    ✅ 已处理: {final_title[:30]}...")

                    # 进度显示
                    if self.processed_articles % 5 == 0:
                        print(f"📝 进度: 已处理{self.processed_articles}篇文章")
                else:
                    print(f"    ⚠️ 跳过: {article['title'][:30]}... (内容提取失败)")

                # 防止请求过快
                time.sleep(0.5)

            page += 1

 

        # 增强Excel保存功能 - 无论是否有数据都保存
        print(f"🔍 准备保存 {len(all_articles)} 条记录到Excel...")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(self.output_file)
        if not output_dir:
            output_dir = "spiderdata"
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            full_path = os.path.abspath(os.path.join(output_dir, os.path.basename(self.output_file)))
            
            if all_articles:
                df = pd.DataFrame(all_articles)
                
                # 确保文件名正确
                if not full_path.endswith('.xlsx'):
                    full_path = full_path.rsplit('.', 1)[0] + '.xlsx'
                
                print(f"💾 正在保存到：{full_path}")
                
                # 使用ExcelWriter保存，确保目录存在
                with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='组织部工作动态', index=False)
                    
                    # 如果定义了Alignment，则应用格式
                    if Alignment:
                        worksheet = writer.sheets['组织部工作动态']
                        for column in worksheet.columns:
                            max_length = 0
                            column_letter = column[0].column_letter
                            for cell in column:
                                try:
                                    if len(str(cell.value)) > max_length:
                                        max_length = len(str(cell.value))
                                except:
                                    pass
                            adjusted_width = min(max_length + 2, 50)
                            worksheet.column_dimensions[column_letter].width = adjusted_width
                
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
                    print(f"✅ Excel文件保存成功！大小：{file_size} 字节")
                    print(f"📁 完整路径：{full_path}")
                else:
                    print(f"❌ 文件未找到：{full_path}")
                    
            else:
                print("⚠️ 无数据，创建带字段的空Excel文件")
                df = pd.DataFrame(columns=['标题', '发布日期', '原日期', '链接', '正文', '字数'])
                
                # 确保文件名正确
                if not full_path.endswith('.xlsx'):
                    full_path = full_path.rsplit('.', 1)[0] + '.xlsx'
                    
                with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='组织部工作动态', index=False)
                    print(f"✅ 空Excel文件已创建：{full_path}")
                    
        except PermissionError as e:
            print(f"❌ 文件权限错误：{e}")
            print("💡 建议：")
            print("   1. 关闭已打开的Excel文件")
            print("   2. 检查文件是否被其他程序占用")
            print("   3. 以管理员身份运行程序")
            
        except Exception as e:
            print(f"❌ Excel保存失败：{str(e)}")
            print(f"📊 详细错误：{type(e).__name__}")
            
            # 创建CSV备份
            try:
                csv_path = full_path.replace('.xlsx', '_backup.csv')
                backup_df = pd.DataFrame(all_articles) if all_articles else pd.DataFrame(columns=['标题', '发布日期', '原日期', '链接', '正文', '字数'])
                backup_df.to_csv(csv_path, encoding='utf-8-sig', index=False)
                print(f"✅ CSV备份文件已创建：{csv_path}")
            except Exception as csv_err:
                print(f"🚨 CSV备份也失败：{csv_err}")
                
                # 最后的文本保存尝试
                try:
                    txt_path = full_path.replace('.xlsx', '_debug.txt')
                    with open(txt_path, 'w', encoding='utf-8') as f:
                        if all_articles:
                            for idx, article in enumerate(all_articles, 1):
                                f.write(f"#{idx} 标题: {article.get('标题', '无标题')}\n")
                                f.write(f"   链接: {article.get('链接', '无链接')}\n")
                                f.write(f"   发布日期: {article.get('发布日期', '无日期')}\n\n")
                        else:
                            f.write("无文章数据可保存\n")
                    print(f"✅ 文本文件已创建：{txt_path}")
                except Exception as final_error:
                    print(f"🚨 所有保存方式都失败：{final_error}")

        return all_articles

if __name__ == '__main__':
    spider = GzdtSpider()
    articles = spider.crawl_all_pages()
    print(f"🎉 爬取完成！共{len(articles or [])}条记录已保存")